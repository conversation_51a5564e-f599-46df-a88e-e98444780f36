<template>
  <uni-popup ref="popup" type="bottom" :mask-click="false" @change="change">
    <view class="payment-dialog">
      <!-- 头部标题 -->
      <view class="dialog-header">
        <text class="dialog-title">钱款去向</text>
        <view class="close-btn" @click="close">
          <view class="iconfont icon-owner-guanbi"></view>
        </view>
      </view>

      <!-- Tab切换 -->
      <view class="tab-container">
        <view class="tab-item" :class="{ active: activeTab === 'deposit' }" @click="switchTab('deposit')">
          <text>订金</text>
        </view>
        <!-- <view class="tab-item" :class="{ active: activeTab === 'freight' }" @click="switchTab('freight')">
          <text>运费</text>
        </view> -->
      </view>

      <!-- 内容区域 -->
      <view class="dialog-content">
        <!-- 订金内容 -->
        <view v-if="activeTab === 'deposit'" class="tab-content">
          <!-- 仲裁完成提示 -->
          <view v-if="arbitrationReason || amountRemarks || (amountImages && amountImages.length > 0)" class="arbitration-notice">
            <text class="notice-text">仲裁已完成，订金将会根据平台的仲裁规则进行处理，感谢您的信任与支持，如有问题请致电客服</text>
          </view>

          <!-- 原因 -->
          <view v-if="arbitrationReason" class="reason-section">
            <text class="reason-label">原因：</text>
            <text class="reason-text">{{ arbitrationReason }}</text>
          </view>

          <!-- 状态显示 -->
          <view class="status-section">
            <view class="status-icon success">
              <view class="iconfont icon-owner-duigou"></view>
            </view>
            <text class="status-text">{{ reversedDepositTimeline[0]?.process || '暂无状态' }}</text>
          </view>

          <!-- 金额显示 -->
          <view class="amount-section">
            <text class="amount-label">订金：</text>
            <text class="amount-value">¥{{ depositInfo.amount }}</text>
          </view>

          <!-- 详情描述 -->
          <view v-if="amountRemarks" class="remarks-section">
            <view class="remarks-title">详情描述</view>
            <view class="remarks-content">
              <text class="remarks-text">{{ amountRemarks }}</text>
            </view>
          </view>

          <!-- 图片 -->
          <view v-if="amountImages && amountImages.length > 0" class="images-section">
            <view class="images-title">图片</view>
            <view class="images-grid">
              <view v-for="(image, index) in amountImages" :key="index" class="image-item" @click="previewImage(image, index)">
                <image :src="image" mode="aspectFill" class="image"></image>
              </view>
            </view>
          </view>

          <!-- 时间线 -->
          <view class="timeline-section">
            <template v-if="reversedDepositTimeline && reversedDepositTimeline.length > 0">
              <view v-for="(item, index) in reversedDepositTimeline" :key="index" class="timeline-item" :class="{ 'is-first': index === 0 }">
                <view class="timeline-left">
                  <view class="timeline-dot" :class="{ 'is-active': index === 0 }"></view>
                </view>
                <view class="timeline-content">
                  <view class="timeline-title">
                    <text v-if="!hasAmount(item.process)">{{ item.process }}</text>
                    <view v-else class="amount-title">
                      <text>{{ splitTitle(item.process).prefix }}</text>
                      <text class="amount-highlight">¥{{ splitTitle(item.process).amount }}</text>
                    </view>
                  </view>
                  <text class="timeline-time">{{ formatDateTime(item.createTime) }}</text>
                </view>
              </view>
            </template>
            <view v-else class="timeline-empty">
              <text class="empty-text">暂无订金时间线数据</text>
            </view>
          </view>
        </view>

        <!-- 运费内容 -->
        <view v-if="activeTab === 'freight'" class="tab-content freight-content">
          <!-- 状态显示 -->
          <view class="status-section">
            <view class="status-icon warning">
              <view class="iconfont icon-owner-tishi"></view>
            </view>
            <text class="status-text">待货主支付</text>
          </view>

          <!-- 提示信息 -->
          <view class="notice-section">
            <text class="notice-text">若无需通过平台支付，可选择已线下收款</text>
          </view>

          <!-- 金额显示 -->
          <view class="amount-section">
            <text class="amount-label">运费：</text>
            <text class="amount-value warning">¥{{ freightInfo.amount }}</text>
          </view>

          <!-- 运费时间线 -->
          <view class="timeline-section">
            <template v-if="reversedFreightTimeline && reversedFreightTimeline.length > 0">
              <view v-for="(item, index) in reversedFreightTimeline" :key="index" class="timeline-item" :class="{ 'is-first': index === 0 }">
                <view class="timeline-left">
                  <view class="timeline-dot" :class="{ 'is-active': index === 0 }"></view>
                </view>
                <view class="timeline-content">
                  <view class="timeline-title">
                    <text v-if="!hasAmount(item.process)">{{ item.process }}</text>
                    <view v-else class="amount-title">
                      <text>{{ splitTitle(item.title).prefix }}</text>
                      <text class="amount-highlight">¥{{ splitTitle(item.title).amount }}</text>
                    </view>
                  </view>
                  <text class="timeline-time">{{ item.time }}</text>
                </view>
              </view>
            </template>
            <view v-else class="timeline-empty">
              <text class="empty-text">暂无运费时间线数据</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="dialog-footer">
        <view v-if="activeTab === 'freight'" class="footer-buttons">
          <view class="btn-secondary" @click="handleOfflinePayment">
            <text>已线下收款</text>
          </view>
          <view class="btn-primary" @click="handleCollectFreight">
            <text>收运费</text>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { formatDateTime } from '../../utils/common'

// 定义props
const props = defineProps({
  // 订金信息
  depositInfo: {
    type: Object,
    default: () => ({
      amount: '100',
      timeline: [
        { title: '成功支付给货主', time: '2025.03.14 12:22' },
        { title: '支付处理中', time: '2025.03.14 12:22' },
        { title: '已到达订金自动流转时间，发起给货主¥100', time: '2025.03.14 12:22' },
        { title: '您已支付订金，平台监管中', time: '2025.03.12 13:14' }
      ]
    })
  },
  // 运费信息
  freightInfo: {
    type: Object,
    default: () => ({
      amount: '2400',
      timeline: [
        { title: '待货主支付', time: '2025.03.14 12:22' },
        { title: '运费支付处理中', time: '2025.03.14 12:22' },
        { title: '已发起运费收款申请¥2400', time: '2025.03.14 12:22' },
        { title: '货物已送达，等待货主确认', time: '2025.03.12 13:14' }
      ]
    })
  },
  // 仲裁原因
  arbitrationReason: {
    type: String,
    default: ''
  },
  // 金额备注/详情描述
  amountRemarks: {
    type: String,
    default: ''
  },
  // 金额相关图片
  amountImages: {
    type: Array,
    default: () => []
  }
})

// 定义emit事件
const emit = defineEmits(['close', 'offline-payment', 'collect-freight'])

// 弹窗引用
const popup = ref(null)

// 当前激活的tab
const activeTab = ref('freight')

// 计算属性：倒序的订金时间线
const reversedDepositTimeline = computed(() => {
  if (!props.depositInfo.timeline || !Array.isArray(props.depositInfo.timeline)) {
    return []
  }
  return [...props.depositInfo.timeline].reverse()
})

// 计算属性：倒序的运费时间线
const reversedFreightTimeline = computed(() => {
  if (!props.freightInfo.timeline || !Array.isArray(props.freightInfo.timeline)) {
    return []
  }
  return [...props.freightInfo.timeline].reverse()
})

// 工具函数：判断标题是否包含金额
const hasAmount = (title: string) => {
  return title.includes('\u00A5')
}

// 工具函数：分割标题文本
const splitTitle = (title: string) => {
  const parts = title.split('\u00A5')
  return {
    prefix: parts[0],
    amount: parts[1]
  }
}

// 打开弹窗方法
const open = (tab = 'freight') => {
  activeTab.value = tab
  popup.value.open()
}

// 关闭弹窗方法
const close = () => {
  popup.value.close()
  emit('close')
}

// 切换tab
const switchTab = (tab: string) => {
  activeTab.value = tab
}

// 弹窗状态变化
const change = (e) => {
  // 可以在这里添加弹窗状态变化的处理逻辑
}

// 处理线下收款
const handleOfflinePayment = () => {
  emit('offline-payment')
  close()
}

// 处理收运费
const handleCollectFreight = () => {
  emit('collect-freight')
  close()
}

// 图片预览
const previewImage = (currentImage: string, currentIndex: number) => {
  uni.previewImage({
    urls: props.amountImages,
    current: currentIndex
  })
}

// 将方法暴露给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.payment-dialog {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
  position: relative;

  // 安全区域背景覆盖
  &::after {
    content: '';
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: env(safe-area-inset-bottom);
    background-color: #fff;
    z-index: -1;
  }

  // 头部区域
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .dialog-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 32rpx;
        color: #999;
      }
    }
  }

  // Tab区域
  .tab-container {
    display: flex;
    background-color: #f8f9fa;
    margin: 0 32rpx;
    border-radius: 12rpx;
    padding: 8rpx;
    margin-top: 24rpx;

    .tab-item {
      flex: 1;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
      transition: all 0.3s;

      text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
      }

      &.active {
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        text {
          color: #333;
          font-weight: 600;
        }
      }
    }
  }

  // 内容区域
  .dialog-content {
    padding: 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .tab-content {
      .arbitration-notice {
        background-color: #fff2f0;
        border: 2rpx solid #ffccc7;
        border-radius: 12rpx;
        padding: 24rpx;
        margin-bottom: 30rpx;

        .notice-text {
          font-size: 28rpx;
          color: #ff4d4f;
          line-height: 1.6;
        }
      }

      .reason-section {
        background-color: #fff2f0;
        border: 2rpx solid #ffccc7;
        border-radius: 12rpx;
        padding: 20rpx 24rpx;
        margin-bottom: 30rpx;
        display: flex;
        align-items: flex-start;

        .reason-label {
          font-size: 28rpx;
          color: #ff4d4f;
          font-weight: 600;
          margin-right: 10rpx;
          flex-shrink: 0;
        }

        .reason-text {
          font-size: 28rpx;
          color: #ff4d4f;
          line-height: 1.5;
          flex: 1;
        }
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 16rpx;
        margin-bottom: 24rpx;

        .status-icon {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .iconfont {
            font-size: 24rpx;
            color: #fff;
          }

          &.success {
            background-color: #52c41a;

            .iconfont {
              font-size: 28rpx;
              font-weight: 600;
            }
          }

          &.warning {
            background-color: #faad14;
          }
        }

        .status-text {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .notice-section {
        background-color: #fef2f2;
        padding: 20rpx;
        border-radius: 12rpx;
        margin-bottom: 24rpx;

        .notice-text {
          font-size: 26rpx;
          color: #e84138;
          line-height: 1.5;
        }
      }

      .amount-section {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;

        .amount-label {
          font-size: 28rpx;
          color: #666;
        }

        .amount-value {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;

          &.warning {
            color: #e84138;
          }
        }
      }

      .remarks-section {
        margin-top: 30rpx;

        .remarks-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
          padding: 20rpx 0;
          border-top: 2rpx solid #ff4d4f;
          border-bottom: 2rpx solid #ff4d4f;
        }

        .remarks-content {
          background-color: #fff2f0;
          border: 2rpx solid #ffccc7;
          border-radius: 12rpx;
          padding: 24rpx;

          .remarks-text {
            font-size: 28rpx;
            color: #333;
            line-height: 1.6;
          }
        }
      }

      .images-section {
        margin-top: 30rpx;

        .images-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
          padding: 20rpx 0;
          border-top: 2rpx solid #ff4d4f;
          border-bottom: 2rpx solid #ff4d4f;
        }

        .images-grid {
          background-color: #fff2f0;
          border: 2rpx solid #ffccc7;
          border-radius: 12rpx;
          padding: 24rpx;
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .image-item {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            overflow: hidden;
            background-color: #f5f5f5;

            .image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .timeline-section {
        position: relative;

        .timeline-item {
          display: flex;
          align-items: flex-start;
          gap: 20rpx;
          position: relative;
          min-height: 80rpx;
          padding-bottom: 24rpx;

          // 连接线伪元素
          &::before {
            content: '';
            position: absolute;
            left: 7rpx;
            top: 16rpx;
            width: 1rpx;
            height: calc(100% + 24rpx - 24rpx);
            background-color: #e0e0e0;
            z-index: 1;
          }

          &:last-child {
            padding-bottom: 0;

            &::before {
              display: none;
            }
          }

          &.is-first {
            .timeline-dot {
              background-color: #52c41a;
              box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
              animation: pulse 2s infinite;
            }
          }

          .timeline-left {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            width: 16rpx;
            flex-shrink: 0;

            .timeline-dot {
              width: 16rpx;
              height: 16rpx;
              background-color: #e84138;
              border-radius: 50%;
              position: relative;
              z-index: 2;

              &.is-active {
                background-color: #52c41a;
                box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
                animation: pulse 2s infinite;
              }
            }
          }

          .timeline-content {
            flex: 1;
            min-height: 56rpx;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            margin-top: -16rpx;

            .timeline-title {
              display: block;
              font-size: 28rpx;
              color: #333;
              line-height: 1.5;
              margin-bottom: 8rpx;

              .amount-title {
                display: flex;
                flex-direction: column;
                gap: 4rpx;

                .amount-highlight {
                  font-weight: 600;
                  color: #e84138;
                  font-size: 30rpx;
                }
              }
            }

            .timeline-time {
              font-size: 24rpx;
              color: #999;
              margin-top: auto;
            }
          }
        }
      }
    }

    // 运费内容特殊样式
    &.freight-content {
      .timeline-item.is-first {
        .timeline-dot.is-active {
          background-color: #faad14;
          box-shadow: 0 0 0 4rpx rgba(250, 173, 20, 0.2);
          animation: pulse-warning 2s infinite;
        }
      }
    }
  }

  // 底部按钮区域
  .dialog-footer {
    padding: 24rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;
    background-color: #fff;

    .footer-buttons {
      display: flex;
      gap: 24rpx;

      .btn-secondary,
      .btn-primary {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12rpx;

        text {
          font-size: 32rpx;
          font-weight: 500;
        }
      }

      .btn-secondary {
        background-color: #f8f9fa;
        border: 1rpx solid #e0e0e0;

        text {
          color: #666;
        }
      }

      .btn-primary {
        background-color: #e84138;

        text {
          color: #fff;
        }
      }
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
  }

  50% {
    box-shadow: 0 0 0 8rpx rgba(82, 196, 26, 0.1);
  }

  100% {
    box-shadow: 0 0 0 4rpx rgba(82, 196, 26, 0.2);
  }
}

// 橙色脉冲动画
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 4rpx rgba(250, 173, 20, 0.2);
  }

  50% {
    box-shadow: 0 0 0 8rpx rgba(250, 173, 20, 0.1);
  }

  100% {
    box-shadow: 0 0 0 4rpx rgba(250, 173, 20, 0.2);
  }
}

// 时间线空状态样式
.timeline-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 20rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999999;
    text-align: center;
  }
}
</style>
