<template>
  <view class="personal-page">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn><text class="iconfont icon-owner-a-fan<PERSON><PERSON><PERSON>ji<PERSON><PERSON>"></text></template>
      <template #title>{{ '' }}</template>
      <template #rightBtn>
        {{ '' }}
      </template>
    </cu-header>
    <view class="main-container">
      <view class="base-info">
        <view class="head">
          <image src="../../static/images/head.png"></image>
        </view>
        <view class="phone-box">
          <view class="owner-name">
            <text>师傅</text>
          </view>
          <view class="owner-phone">
            <text>18231319486</text>
          </view>
        </view>
        <view class="edit-btn">
          <view class="iconfont icon-owner-bianji" @click="toEdit"></view>
        </view>
      </view>
      <view class="card-info-box">
        <view class="card-info">
          <view class="title">
            <text>常用工具</text>
          </view>
          <view class="tool-box">
            <view class="tool-box-item" v-for="item in tabList" :key="item.id" @click="toEditDetail(item.id)">
              <image class="tool-image" :src='item.url' />
              <view>
                <text>{{ item.text }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 退出登录区域 -->
      <view class="logout-container">
        <view class="logout-card">
          <view class="logout-btn" @click="handleLogout">
            <text class="logout-text">退出登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { clearStorage } from '@/utils/storage'
const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
const phoneNum = ref('')
const tabList = [
  {
    id: 1,
    text: '我的车队',
    url: '../../static/images/car-team.png'
  },
  {
    id: 2,
    text: '运力助手',
    url: '../../static/images/car-info.png'
  },
  {
    id: 3,
    text: '修改号码',
    url: '../../static/images/dianhua.png'
  },
  {
    id: 4,
    text: '联系客服',
    url: '../../static/images/connect.png'
  },
]

const toEdit = () => {
  uni.reLaunch({
    url: './editDetail'
  })
}
const toEditDetail = (id: number) => {
  if (id == 1) {
    uni.reLaunch({
      url: '/pageComDriver/my/carTeam'
    })
  } else if (id == 2) {
    uni.reLaunch({
      url: ''
    })
  } else if (id == 3) {
    uni.reLaunch({
      url: '../../pageCom/my/editPhone'
    })
  } else {
    uni.makePhoneCall()
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    confirmText: '退出',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        // 清除所有缓存
        clearStorage()

        // 显示退出成功提示
        uni.showToast({
          title: '退出成功',
          icon: 'success',
          duration: 1500
        })

        // 延迟跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login'
          })
        }, 1500)
      }
    }
  })
}
</script>

<style lang="scss" scoped>
.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

:deep(.cu-header-box) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  background-size: 100% 400rpx;
  z-index: 3;

  .header-container {
    background-color: transparent;
  }

  .cu-header {
    background-color: transparent;
  }
}

.main-container {
  width: 100%;
  position: relative;
  z-index: 1;
  padding: 20rpx 0rpx 0rpx 0rpx;
  box-sizing: border-box;
  padding-top: v-bind(heightHeader);
}

.base-info {
  width: 100%;
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding-top: 20rpx;
  padding-bottom: 20rpx;

  .head {
    width: 150rpx;
    height: 150rpx;
    border-radius: 50%;
    margin: 0 20rpx;

    image {
      width: 150rpx;
      height: 150rpx;
      border-radius: 50%;
    }
  }

  .phone-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 40rpx;

    .owner-name {
      font-size: 14rpx;
    }

    .owner-Phone {
      font-size: 12rpx;
    }

    text {
      font-size: $uni-font-size-lg;
      color: #333;
      margin-right: 8rpx;
      font-weight: 700;
    }

    .iconfont {
      font-size: $uni-font-size-base;
      color: #333;
    }
  }

  .edit-btn {
    margin-top: 50rpx;
    position: absolute;
    right: 20rpx;
  }
}

.card-info-box {
  padding: 0rpx 30rpx;
  box-sizing: border-box;

  .card-info {
    width: 100%;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16rpx;
    margin-top: 20rpx;

    .title {
      font-size: 36rpx;
    }

    .tool-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      margin-top: 20rpx;

      .tool-box-item {
        width: 25%;
        text-align: center;
        margin-bottom: 20rpx;

        .tool-image {
          width: 100rpx;
          height: 100rpx;
        }

        text {
          margin-top: 20rpx;
        }
      }

    }
  }
}
</style>
